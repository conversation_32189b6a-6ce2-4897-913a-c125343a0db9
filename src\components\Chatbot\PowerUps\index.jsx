import { useEffect, useState } from 'react';

import { v4 as uuidv4 } from 'uuid';


import DButtonIcon from '../../Global/DButtonIcon';
import DCheckbox from '../../Global/DCheckbox';
import DDraggableContainer from '../../Global/DDraggableContainer';
import DInput from '../../Global/DInput/DInput';

import DSelect from '../../Global/DSelect';
import DSelectSearch from '../../Global/DSelectSearch';
import DSwitchAccordion from '../../Global/DSwitchAccordion';
import DTooltip from '../../Global/DTooltip';
import AddIcon from '../../Global/Icons/AddIcon';
import CopyIcon from '../../Global/Icons/CopyIcon';
import LinkIcon from '../../Global/Icons/LinkIcon';
import DImageInput from '../../Global/DImageInput';
import {
  DAYS_OF_WEEK,
  LIVE_AGENT_AVAILABILITY,
  TIME_OPTIONS,
} from '@/constants';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import clsx from 'clsx';
import DTransition from '@/components/Global/DTransition';
import LeadGenInput from '@/components/Global/LeadGenInput';
import ValidationError from '@/components/Global/ValidationError';

import featureCheck from '@/helpers/tier/featureCheck';
import InfoIcon from '@/components/Global/Icons/InfoIcon';
import useToast from '@/hooks/useToast';
import DButton from '@/components/Global/DButton';
import { getDNSRecords } from '@/services/chatbot.service';
import { useParams } from 'react-router-dom';
import CheckmarkIcon from '@/components/Global/Icons/CheckmarkIcon';
import * as userService from '@/services/user.service';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import DSwitch from '@/components/Global/DSwitch';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import { activateNewDesign } from '@/services/chatbot.service';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';

const PowerUps = ({ customizationData, updateCustomizationData, errorLeadGenFields, setErrorLeadGenFields, onCustomUrlErrorChange, dropdownValidationErrors = {} }) => {
  const params = useParams();
  const [availability, setAvailability] = useState(
    LIVE_AGENT_AVAILABILITY.ALWAYS
  );
  const [allowUserSkip, setAllowUserSkip] = useState(
    customizationData?.is_data_collection_optional
  );
  const [showCustomAvailability, setShowCustomAvailability] = useState(false);
  const [selectedDays, setSelectedDays] = useState(
    customizationData.live_agent_list_of_schedules
  );
  const [liveAgentVisibility, setLiveAgentVisibility] = useState(
    customizationData.live_agent_always_active
      ? 'show_always'
      : customizationData.show_data_collection_on_conversation_begin
      ? 'show_on_start'
      : customizationData.show_live_agent_rule
      ? 'show_based_on_rule'
      : 'show_always'
  );

  const [leadGenVisibility, setLeadGenVisibility] = useState(
    customizationData.show_data_collection_on_conversation_begin
      ? 'show_start'
      : 'show_based_on_rule'
  );

  const [dnsRecords, setDnsRecords] = useState(
    customizationData?.dns_values || []
  );
  const [loading, setLoading] = useState(false);
  const [copiedDnsRecord, setCopiedDnsRecord] = useState(null);
  const [loadingVoices, setLoadingVoices] = useState(false);
  const teamSelected = useTeamManagementStore((state) => state.selectedTeam?.id);
  const { addWarningToast } = useToast();
  const [openActivateNewDesign, setOpenActivateNewDesign] = useState(false);
  const [activateNewDesignLoading, setActivateNewDesignLoading] = useState(false);
  const [customUrlError, setCustomUrlError] = useState('');
  const setSelectedChatbot = useChatbotStore(
    (state) => state.setSelectedChatbot
  );
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);


const TIMEZONE_OPTIONS = Intl.supportedValuesOf('timeZone').map(tz => ({
  // e.g. "Europe/Belgrade" → "Europe – Belgrade"
  label: tz.replace(/_/g, ' ').replace(/\//, ' – '),
  value: tz
}));



  const handleDelete = (id) => {
    const updatedFields = customizationData.data_collection_fields.filter(
      (field) => field.id !== id
    );
    updateCustomizationData('data_collection_fields', updatedFields);
  };

  const handleAddField = () => {
    setErrorLeadGenFields(false);
    const newField = {
      id: uuidv4(),
      input_label: '',
      input_name: '',
      input_type: 'text',
      input_placeholder: '',
      is_mandatory: true,
    };

    // Handle the case where data_collection_fields might be null
    const currentFields = customizationData.data_collection_fields || [];

    updateCustomizationData('data_collection_fields', [
      ...currentFields,
      newField,
    ]);
  };

  const handleEditLeadGenInput = (id, newData) => {
    const updatedFields = customizationData.data_collection_fields.map(
      (field) => (field.id === id ? { ...field, ...newData } : field)
    );

    updateCustomizationData('data_collection_fields', updatedFields);
  };

  const copyToClipboard = (value) => {
    setCopiedDnsRecord(value);
    navigator.clipboard.writeText(value);
    setTimeout(() => {
      setCopiedDnsRecord(null);
    }, 2000);
  };

  const handleFaviconChange = async (event) => {
    const file = event.target.files[0];
    let formData = new FormData();
    if (file) {
      formData.append('file', file);
      const response = await userService.uploadFile(formData);
      if (response.status === 200) {
        updateCustomizationData('custom_url_favicon', response.data.url);
      }
    } else {
      updateCustomizationData('custom_url_favicon', '');
    }
  };

  const handleAgentAvailabilityChange = (value) => {
    if (value === 'always') {
      updateCustomizationData('live_agent_always_available', true);
      updateCustomizationData('live_agent_office_hours', false);
      updateCustomizationData('live_agent_custom_schedule', false);
      updateCustomizationData('live_agent_always_active', true);
      updateCustomizationData(
        'live_agent_availability',
        LIVE_AGENT_AVAILABILITY.ALWAYS
      );
      setShowCustomAvailability(false);
      setAvailability(LIVE_AGENT_AVAILABILITY.ALWAYS);
    } else if (value === 'office_hours') {
      updateCustomizationData('live_agent_always_available', false);
      updateCustomizationData('live_agent_office_hours', true);
      updateCustomizationData('live_agent_custom_schedule', false);
      updateCustomizationData('live_agent_always_active', false);
      updateCustomizationData('live_agent_list_of_schedules', [
        { day: 'Monday', from: '09:00', to: '17:00' },
        { day: 'Tuesday', from: '09:00', to: '17:00' },
        { day: 'Wednesday', from: '09:00', to: '17:00' },
        { day: 'Thursday', from: '09:00', to: '17:00' },
        { day: 'Friday', from: '09:00', to: '17:00' },
      ]);
      updateCustomizationData(
        'live_agent_availability',
        LIVE_AGENT_AVAILABILITY.OFFICE_HOURS
      );
      setAvailability(LIVE_AGENT_AVAILABILITY.OFFICE_HOURS);
      setShowCustomAvailability(false);
    } else if (value === 'custom') {
      updateCustomizationData('live_agent_always_available', false);
      updateCustomizationData('live_agent_office_hours', false);
      updateCustomizationData('live_agent_custom_schedule', true);
      updateCustomizationData('live_agent_always_active', false);
      setShowCustomAvailability(true);
      updateCustomizationData(
        'live_agent_availability',
        LIVE_AGENT_AVAILABILITY.CUSTOM
      );
      setAvailability(LIVE_AGENT_AVAILABILITY.CUSTOM);
    }
  };

  const handleDayChange = (dayIndex, field, value) => {
    // Create a new array with new object references
    const updatedDays = selectedDays.map(day => {
      if (day.day === dayIndex.day) {
        // Create a new object for the modified day
        return { ...day, [field]: value };
      }
      return { ...day }; // Create new references for other days too
    });
    
    setSelectedDays(updatedDays);
    updateCustomizationData('live_agent_list_of_schedules', updatedDays);
  };

  const handleGetDNSRecords = async () => {
    setLoading(true);
    try {
      const response = await getDNSRecords(
        params.id,
        customizationData.custom_url
      );
      if (response.status === 200) {
        setDnsRecords(response.data.dns_values);
        updateCustomizationData('dns_values', response.data.dns_values);
        setLoading(false);
      }
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  const handleActivateNewDesign = async () => {
    try {
      setActivateNewDesignLoading(true);
      const response = await activateNewDesign(customizationData?.kb_id);
      if (response.status === 200) {
        setSelectedChatbot({
          ...selectedChatbot,
          knowledge_base: {
            ...selectedChatbot.knowledge_base,
            new_design_activated: true,
          },
        });
        setOpenActivateNewDesign(false);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setActivateNewDesignLoading(false);
    }
  };

  const validateCustomUrl = (url) => {
    if (!url || url.trim() === '') {
      setCustomUrlError('');
      onCustomUrlErrorChange && onCustomUrlErrorChange('');
      return true;
    }

    const cleanUrl = url.replace(/^https?:\/\//, '').trim();

    if (cleanUrl.toLowerCase().includes('dante-ai')) {
      const error = 'Custom URLs cannot contain "dante-ai"';
      setCustomUrlError(error);
      onCustomUrlErrorChange && onCustomUrlErrorChange(error);
      return false;
    }

    const domainPattern = /^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?\.([a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?\.)*[a-zA-Z]{2,}$/;

    if (!domainPattern.test(cleanUrl)) {
      const error = 'Please use the format: subdomain.domain.com';
      setCustomUrlError(error);
      onCustomUrlErrorChange && onCustomUrlErrorChange(error);
      return false;
    }

    if (url.includes('://')) {
      const error = 'Please use the format: subdomain.domain.com (without https://)';
      setCustomUrlError(error);
      onCustomUrlErrorChange && onCustomUrlErrorChange(error);
      return false;
    }

    setCustomUrlError('');
    onCustomUrlErrorChange && onCustomUrlErrorChange('');
    return true;
  };

  const handleCustomUrlChange = (value) => {
    updateCustomizationData('custom_url', value);
    validateCustomUrl(value);
  };


  useEffect(() => {
    if (customizationData?.dns_values) {
      setDnsRecords(customizationData?.dns_values);
    }
  }, [customizationData?.dns_values]);

  useEffect(() => {
    if (customizationData?.live_agent_list_of_schedules) {
      setSelectedDays(customizationData?.live_agent_list_of_schedules);
    }
  }, [customizationData?.live_agent_list_of_schedules]);

  useEffect(() => {
    if (customizationData?.custom_url) {
      validateCustomUrl(customizationData.custom_url);
    } else {
      // Clear error when no custom URL is set
      setCustomUrlError('');
      onCustomUrlErrorChange && onCustomUrlErrorChange('');
    }
  }, [customizationData?.custom_url]);

  return (
    <div className="flex flex-col gap-size2">
      {!selectedChatbot?.knowledge_base?.new_design_activated && (
        <div className="flex flex-col gap-size1 mb-size2">
          <div className="flex items-center gap-size0">
            <DSwitch
              label="Activate new design"
              onChange={() => setOpenActivateNewDesign(true)}
              id="activate-new-design"
            />
            <DTooltip content="By enabling the new design, your AI Chatbot will adopt a refreshed appearance and gain access to exciting new features. If you'd prefer to stick with the old design, you can continue using it until <b>31 December</b>. To keep the old design, make sure not to toggle this option on.">
              <InfoIcon className="text-grey-50 size-3 ml-1" />
            </DTooltip>
          </div>
          <p className="text-xs tracking-tight text-grey-50">
            Activate the new design to upgrade your AI Chatbot's look. The
            changes shown below will only take effect once your activate
            this setting.
          </p>
        </div>
      )}
      <DSwitchAccordion
        title="Human handover"
        tooltip={true}
        tooltipContent="Allow human handover to seamlessly take over chats for complex queries, ensuring real-time support with full context. This feature will be enabled on shared links."
        switchOpen={customizationData.talk_to_live_agent}
        onToggle={(value) => {
          if (!teamSelected && !featureCheck('live_agent')) {
            return;
          }
          updateCustomizationData('talk_to_live_agent', value);

           // if (value) {
          //   handleAgentAvailabilityChange(LIVE_AGENT_AVAILABILITY.OFFICE_HOURS);
          // }
          
        }}
      >
        <div className="flex flex-col gap-size5">
          <div className="flex flex-col gap-size2">
            <span className="text-base font-medium tracking-tight">
              Availability
            </span>
            <DSelect
              value={
                customizationData?.live_agent_always_available
                  ? LIVE_AGENT_AVAILABILITY.ALWAYS
                  : customizationData?.live_agent_office_hours
                  ? LIVE_AGENT_AVAILABILITY.OFFICE_HOURS
                  : customizationData?.live_agent_custom_schedule
                  ? LIVE_AGENT_AVAILABILITY.CUSTOM
                  : ''
              }
              onChange={handleAgentAvailabilityChange}
              options={[
                {
                  value: LIVE_AGENT_AVAILABILITY.ALWAYS,
                  label: 'Always available',
                },
                {
                  value: LIVE_AGENT_AVAILABILITY.OFFICE_HOURS,
                  label: 'Office hours',
                },
                { value: LIVE_AGENT_AVAILABILITY.CUSTOM, label: 'Custom' },
              ]}
              placeholder="Select an availability option"
              error={dropdownValidationErrors.availability}
            />
            {dropdownValidationErrors.availability && (
              <ValidationError error={dropdownValidationErrors.availability} />
            )}

            <DTransition
              show={
                showCustomAvailability ||
                customizationData.live_agent_availability ===
                  LIVE_AGENT_AVAILABILITY.CUSTOM
              }
              type="collapse"
            >
              <div className="flex flex-col gap-size1">
                <div className="flex gap-size1 w-full">
                  {Array.from({ length: 7 }).map((_, index) => (
                    <button
                      key={DAYS_OF_WEEK[index]}
                      className={clsx(
                        'flex justify-center items-center rounded-full p-size1 w-[30px] h-[30px] bg-purple-50 text-white',
                        selectedDays.some(
                          (day) => day.day === DAYS_OF_WEEK[index]
                        ) && 'border-purple-200 border text-white border-2'
                      )}
                      onClick={() => {
                        if (!selectedDays.some(day => day.day === DAYS_OF_WEEK[index])) {
                          const updatedDays = [
                            ...selectedDays,
                            { day: DAYS_OF_WEEK[index], from: '', to: '' },
                          ];
                          setSelectedDays(updatedDays);
                          updateCustomizationData('live_agent_list_of_schedules', updatedDays);
                        }
                      }}
                    >
                      <span className="text-xs font-refular tracking-tight">
                        {DAYS_OF_WEEK[index].slice(0, 2).toUpperCase()}
                      </span>
                    </button>
                  ))}
                </div>
                <div className="flex flex-col gap-size1 w-full">
                  {selectedDays.map((dayIndex) => (
                    <div
                      key={dayIndex.day}
                      className="flex gap-size2 w-full items-end"
                    >
                      <div className="flex items-center justify-center w-[40px] h-[40px] bg-purple-50 rounded-size0 flex-shrink-0">
                        <span className="text-xs font-medium text-white">
                          {dayIndex.day.slice(0, 2).toUpperCase()}
                        </span>
                      </div>
                      <div className="flex flex-col gap-size1 w-full">
                        <p className="text-xs font-regular tracking-tight">
                          Start time
                        </p>
                        <DSelect
                          options={TIME_OPTIONS}
                          value={
                            selectedDays?.find(
                              (day) => day.day === dayIndex.day
                            )?.from || ''
                          }
                          onChange={(value) =>
                            handleDayChange(dayIndex, 'from', value)
                          }
                        />
                      </div>
                      <div className="flex flex-col gap-size1 w-full">
                        <p className="text-xs font-regular tracking-tight">
                          End time
                        </p>
                        <DSelect
                          options={TIME_OPTIONS}
                          value={
                            selectedDays?.find(
                              (day) => day.day === dayIndex.day
                            )?.to || ''
                          }
                          onChange={(value) =>
                            handleDayChange(dayIndex, 'to', value)
                          }
                        />
                      </div>
                      <DButtonIcon
                        variant="outlined"
                        size="md"
                        className="!size-12 !px-size2"
                        onClick={() =>
                          setSelectedDays((prev) =>
                            prev.filter((day) => day !== dayIndex)
                          )
                        }
                      >
                        <DeleteIcon />
                      </DButtonIcon>
                    </div>
                  ))}
                </div>
              </div>
            </DTransition>
            {/* <DAlert state="info" className="items-center !w-full">
                <p className="text-xs font-regular tracking-tight">
                  Availability slots are managed within Human Handover dashboard.{' '}
                  <a className="text-sm font-medium tracking-tight underline text-purple-200">
                    Edit availability ↗
                  </a>
                </p>
              </DAlert> */}
          </div>
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight">
              Human handover timezone
            </span>
            <DSelectSearch
              id="human-handover-timezone"
              options={TIMEZONE_OPTIONS}
              value={customizationData?.human_handover_timezone || Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone || 'Europe/Belgrade'}
              onChange={(value) =>
                updateCustomizationData('human_handover_timezone', value)
              }
            />
          </div>
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight">
              Message to confirm user's name
            </span>
            <DInput
              placeholder="Before connecting you with a member of our team, please confirm your name."
              value={customizationData?.live_agent_name_prompt || ''}
              onChange={(e) =>
                updateCustomizationData(
                  'live_agent_name_prompt',
                  e.target.value
                )
              }
            />
          </div>
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight">
              Message to confirm user's email
            </span>
            <DInput
              placeholder="Before connecting you with a member of our team, please confirm your email."
              value={customizationData?.live_agent_email_prompt || ''}
              onChange={(e) =>
                updateCustomizationData(
                  'live_agent_email_prompt',
                  e.target.value
                )
              }
            />
          </div>
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight">
              Human handover visibility
            </span>
            <DSelect
              options={[
                // { value: 'show_always', label: 'Show always' },
                { value: 'show_on_start', label: 'Show on start' },
                { value: 'show_based_on_rule', label: 'Show based on rule' },
              ]}
              value={
                customizationData?.show_live_agent_on_conversation_begin
                  ? 'show_on_start'
                  : customizationData?.show_live_agent_rule
                  ? 'show_based_on_rule'
                  : ''
              }
              onChange={(value) => {
                if (value === 'show_always') {
                  updateCustomizationData('live_agent_always_active', true);
                  updateCustomizationData('show_live_agent_rule', '');
                  updateCustomizationData(
                    'show_live_agent_on_conversation_begin',
                    false
                  );
                  setLiveAgentVisibility('show_always');
                } else if (value === 'show_on_start') {
                  updateCustomizationData('live_agent_always_active', false);
                  updateCustomizationData('show_live_agent_rule', '');
                  updateCustomizationData(
                    'show_live_agent_on_conversation_begin',
                    true
                  );
                  setLiveAgentVisibility('show_on_start');
                } else if (value === 'show_based_on_rule') {
                  updateCustomizationData('live_agent_always_active', false);
                  updateCustomizationData(
                    'show_live_agent_on_conversation_begin',
                    false
                  );
                  setLiveAgentVisibility('show_based_on_rule');
                }
              }}
              placeholder="Select a visibility option"
              error={dropdownValidationErrors.handoverVisibility}
            />
            {dropdownValidationErrors.handoverVisibility && (
              <ValidationError error={dropdownValidationErrors.handoverVisibility} />
            )}

          </div>
          {(liveAgentVisibility === 'show_based_on_rule' ||
            customizationData?.show_live_agent_rule) && (
            <div className="flex flex-col gap-size1 transition-all">
              <span className="text-base font-medium tracking-tight">Rule</span>
              <DInput
                placeholder="Show human handover if user mentions 'human handover'"
                value={customizationData?.show_live_agent_rule || ''}
                onChange={(e) =>
                  updateCustomizationData(
                    'show_live_agent_rule',
                    e.target.value
                  )
                }
              />
            </div>
          )}
        </div>
      </DSwitchAccordion>

      <DSwitchAccordion
        title="AI-lead generation forms"
        tooltip={true}
        tooltipContent="Display a data collection form inside your AI Chatbot. Connect this to your CRM via 'Integrations. This feature will be enabled on shared links."
        switchOpen={customizationData.data_collection}
        onToggle={(value) => {
          if (!teamSelected && !featureCheck('data_collection')) {
            return;
          }
          updateCustomizationData('data_collection', value)
          updateCustomizationData('data_collection_fields', customizationData.data_collection_fields)
        }}
      >
        <div className="flex flex-col gap-size5">
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight">Title</span>
            <DInput
              placeholder="Set form title"
              value={customizationData?.data_collection_title || ''}
              onChange={(e) => {
                updateCustomizationData(
                  'data_collection_title',
                  e.target.value
                );
                updateCustomizationData(
                  'data_collection_fields',
                  customizationData.data_collection_fields
                );
              }}
            />
          </div>
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight">
              User consent text
            </span>
            <DInput
              placeholder="I consent to my data being stored"
              value={customizationData?.data_collection_consent_text || ''}
              onChange={(e) => {
                updateCustomizationData(
                  'data_collection_consent_text',
                  e.target.value
                );
                updateCustomizationData(
                  'data_collection_fields',
                  customizationData.data_collection_fields
                );
              }}
            />
          </div>
          <div className="flex flex-col gap-size0 relative">
            <span className="text-base font-medium tracking-tight">
              Form Fields
            </span>
            <DDraggableContainer
              items={customizationData?.data_collection_fields}
              setItems={(value) =>
                updateCustomizationData('data_collection_fields', value)
              }
              ItemComponent={LeadGenInput}
              onDelete={handleDelete}
              onEdit={handleEditLeadGenInput}
            />
            <button
              className="bg-grey-2 rounded-size2 py-size1 px-size2 text-sm font-medium tracking-tight flex items-center gap-size1"
              onClick={handleAddField}
            >
              <AddIcon />
              <span>Add input field</span>
            </button>
            {errorLeadGenFields && (
              <p className="text-sm text-red-500">
                AI lead gen fields are required when AI lead gen is enabled
              </p>
            )}
          </div>
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight">
              Form Visibility
            </span>
            <DSelect
              value={leadGenVisibility}
              onChange={(value) => {
                if (value === 'show_start') {
                  updateCustomizationData(
                    'show_data_collection_on_conversation_begin',
                    true
                  );
                  setLeadGenVisibility('show_start');
                } else if (value === 'show_based_on_rule') {
                  updateCustomizationData(
                    'show_data_collection_on_conversation_begin',
                    false
                  );
                  setLeadGenVisibility('show_based_on_rule');
                }
                updateCustomizationData(
                  'data_collection_fields',
                  customizationData.data_collection_fields
                );
              }}
              options={[
                { value: 'show_start', label: 'Show at the start' },
                { value: 'show_based_on_rule', label: 'Show based on rule' },
              ]}
            />
            <DCheckbox
              label="Allow the user to skip lead gen input"
              checked={allowUserSkip}
              onChange={() => {
                setAllowUserSkip(!allowUserSkip);
                updateCustomizationData(
                  'is_data_collection_optional',
                  !allowUserSkip
                );
                updateCustomizationData(
                  'data_collection_fields',
                  customizationData.data_collection_fields
                );
              }}
              className="pl-size2"
            />
          </div>
          <DTransition show={leadGenVisibility === 'show_based_on_rule'}>
            <div className="flex flex-col gap-size1">
              <span className="text-base font-medium tracking-tight">Rule</span>
              <DInput
                placeholder="Show lead gen form if user mentions 'lead gen'"
                value={customizationData?.data_collection_rule || ''}
                onChange={(e) => {
                  updateCustomizationData(
                    'data_collection_rule',
                    e.target.value
                  );
                  updateCustomizationData(
                    'data_collection_fields',
                    customizationData.data_collection_fields
                  );
                }}
              />
            </div>
          </DTransition>
        </div>
      </DSwitchAccordion>

      <DSwitchAccordion
        title="Calendar link"
        tooltip={true}
        tooltipContent="Let users instantly book appointments via a Calendly link directly from your chatbot. This feature will be enabled on shared links."
        switchOpen={customizationData.calendly_integration_enabled}
        onToggle={(value) => {
          if (!teamSelected && !featureCheck('calendly_integration')) {
            return;
          }
          updateCustomizationData('calendly_integration_enabled', value);
        }}
      >
        <div className="flex flex-col gap-size5">
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight">
              Set calendar link
            </span>
            <DInput
              placeholder="https://calendly.com/your-calendar-link"
              value={customizationData?.calendly_url || ''}
              onChange={(e) =>
                updateCustomizationData('calendly_url', e.target.value)
              }
              iconPlacement="pre"
              icon={<LinkIcon />}
            />
          </div>
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight">
              Button text
            </span>
            <DInput
              placeholder="Book a meeting"
              value={customizationData?.calendly_btn_text || ''}
              onChange={(e) =>
                updateCustomizationData('calendly_btn_text', e.target.value)
              }
            />
          </div>
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight">
              Calendar visibility
            </span>
            <DSelect
              value={
                customizationData?.show_calendly_at_the_start
                  ? 'show_at_start'
                  : 'show_based_on_rule'
              }
              onChange={(value) => {
                if (value === 'show_at_start') {
                  updateCustomizationData('show_calendly_at_the_start', true);
                } else if (value === 'show_based_on_rule') {
                  updateCustomizationData('show_calendly_at_the_start', false);
                }
              }}
              options={[
                { value: 'show_at_start', label: 'Show at start' },
                { value: 'show_based_on_rule', label: 'Show based on rule' },
              ]}
            />
          </div>
          {!customizationData?.show_calendly_at_the_start && (
            <div className="flex flex-col gap-size1">
              <span className="text-base font-medium tracking-tight">Rule</span>
              <DInput
                placeholder="Show calendar link if user mentions 'calendar'"
                value={customizationData?.show_calendly_rule || ''}
                onChange={(e) =>
                  updateCustomizationData('show_calendly_rule', e.target.value)
                }
              />
            </div>
          )}
        </div>
      </DSwitchAccordion>

      <DSwitchAccordion
        title="Personalize URL"
        tooltip={true}
        tooltipContent="Use a custom domain to make your chatbot URL unique and aligned with your brand. This feature will be enabled on shared links."
        switchOpen={customizationData.custom_url_enabled}
        onToggle={(value) => {
          if (!teamSelected && !featureCheck('custom_url')) {
            return;
          }
          updateCustomizationData('custom_url_enabled', value)
        }}
      >
        <div className="flex flex-col gap-size5">
          <div className="flex flex-col gap-size1">
            <div className="text-base font-medium tracking-tight flex items-center gap-size1">
              Custom URL
              <DTooltip content="Customize the URL of your AI Chatbot">
                <InfoIcon className="size-3 text-grey-20" />
              </DTooltip>
            </div>

            <div className="flex items-center gap-size1">
              <DInput
                iconPlacement="pre"
                icon={<LinkIcon />}
                value={customizationData?.custom_url || ''}
                onChange={(e) => handleCustomUrlChange(e.target.value)}
                placeholder="chat.yoursite.com"
                error={customUrlError}
              />
              <DButton
                variant="outlined"
                onClick={handleGetDNSRecords}
                loading={loading}
                size="sm"
                disabled={!customizationData?.custom_url || customUrlError}
                className="!min-w-36 !h-11 rounded-size1 !border-grey-10"
              >
                Get DNS records
              </DButton>
            </div>

            <div className="text-xs font-regular tracking-tight text-grey-20 bg-grey-5 mt-2 p-size1 rounded-size0">
              Use a custom address from a domain you own, like chat.yoursite.com. Make sure the subdomain you choose is not already used for anything else.
            </div>
          </div>
          <DTransition show={dnsRecords?.length > 0}>
            <div className="flex flex-col gap-size1">
              <span className="text-base font-medium tracking-tight">
                DNS Records
              </span>
              <div className="overflow-x-auto ">
                <table className="w-full table-fixed text-left">
                  <thead>
                    <tr className="border-b border-grey-5">
                      <th className="text-sm font-regular tracking-tight p-size1">
                        Type
                      </th>
                      <th className="text-sm font-regular tracking-tight p-size1">
                        Name
                      </th>
                      <th className="text-sm font-regular tracking-tight p-size1">
                        Value
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {dnsRecords.map((row, i) => (
                      <tr key={i}>
                        <td className="text-sm font-regular tracking-tight p-size1">
                          {row.type}
                        </td>
                        <td className="text-sm font-regular tracking-tight p-size1">
                          <div className="flex gap-size1 justify-between items-center">
                            <span className="max-w-[85%] truncate">
                              {row.name}
                            </span>
                            <DButtonIcon
                              variant="outlined"
                              size="md"
                              onClick={() => copyToClipboard(row.name)}
                            >
                              {copiedDnsRecord === row.name ? (
                                <CheckmarkIcon />
                              ) : (
                                <CopyIcon />
                              )}
                            </DButtonIcon>
                          </div>
                        </td>
                        <td className="text-sm font-regular tracking-tight p-size1">
                          <div className="flex gap-size1 justify-between items-center">
                            <span className="max-w-[85%] truncate">
                              {row.value}
                            </span>
                            <DButtonIcon
                              variant="outlined"
                              size="md"
                              onClick={() => copyToClipboard(row.value)}
                            >
                              {copiedDnsRecord === row.value ? (
                                <CheckmarkIcon />
                              ) : (
                                <CopyIcon />
                              )}
                            </DButtonIcon>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </DTransition>
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight flex items-center gap-size1">
              Page title
              <DTooltip content="Customize the title of your AI Chatbot page">
                <InfoIcon className="size-3 text-grey-20" />
              </DTooltip>
            </span>
            <DInput
              value={customizationData?.custom_url_title || ''}
              onChange={(e) =>
                updateCustomizationData('custom_url_title', e.target.value)
              }
              placeholder="Your page title"
            />
          </div>
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight flex items-center gap-size1">
              Page description
              <DTooltip content="Customize the description of your AI Chatbot page">
                <InfoIcon className="size-3 text-grey-20" />
              </DTooltip>
            </span>
            <DInput
              value={customizationData?.custom_url_meta_description || ''}
              onChange={(e) =>
                updateCustomizationData(
                  'custom_url_meta_description',
                  e.target.value
                )
              }
              placeholder="Add your description here"
            />
          </div>
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight flex items-center gap-size1">
              Favicon
              <DTooltip content="Customize the favicon of your AI Chatbot page">
                <InfoIcon className="size-3 text-grey-20" />
              </DTooltip>
            </span>
            <DImageInput
              onChange={handleFaviconChange}
              value={customizationData?.custom_url_favicon}
            />
            <span className="text-xs font-regular tracking-tight text-grey-20">
              *Supported file types: PNG, JPEG, JPG
            </span>
          </div>
          <div className="flex flex-col gap-size1">
            <span className="text-base font-medium tracking-tight flex items-center gap-size1">
              Meta image URL
              <DTooltip content="Customize the meta image URL of your AI Chatbot page">
                <InfoIcon className="size-3 text-grey-20" />
              </DTooltip>
            </span>
            <DInput
              value={customizationData?.custom_url_meta_image || ''}
              onChange={(e) =>
                updateCustomizationData('custom_url_meta_image', e.target.value)
              }
              placeholder="Add your meta image URL here"
            />
          </div>
        </div>
      </DSwitchAccordion>
      <DConfirmationModal
        open={openActivateNewDesign}
        onClose={() => setOpenActivateNewDesign(false)}
        onConfirm={handleActivateNewDesign}
        title="Activate New Design"
        description="Are you sure you want to activate the new design? This action cannot be undone."
        confirmText="Activate"
        cancelText="Cancel"
        loading={activateNewDesignLoading}
      />
    </div>
  );
};

export default PowerUps;
